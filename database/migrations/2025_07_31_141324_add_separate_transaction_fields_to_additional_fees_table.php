<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('additional_fees', function (Blueprint $table) {
            $table->boolean('is_separate_transaction')->default(false)->after('amount');
            $table->string('transaction_number')->nullable()->after('is_separate_transaction');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('additional_fees', function (Blueprint $table) {
            $table->dropColumn(['is_separate_transaction', 'transaction_number']);
        });
    }
};
