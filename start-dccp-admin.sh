#!/bin/bash

# Change to the project directory
cd /home/<USER>/DccpAdminV2
export APP_ENV=local
export APP_DEBUG=true

# Install/update dependencies if needed
# composer install --no-dev --optimize-autoloader
# npm ci --production

# Clear and cache Laravel configurations
php artisan optimize

# Start the FrankenPHP server
php artisan octane:start --server=frankenphp --host=127.0.0.1 --port=28561 --admin-port=28560 &
# php artisan queue:listen redis --queue=assessments,pdf-generation,default,bulk-student-transfers,student-transfers
#  &
# php artisan nightwatch:agent &
# php artisan     &
php artisan horizon &
wait

# php artisan serve --host=127.0.0.1 --port=28561
