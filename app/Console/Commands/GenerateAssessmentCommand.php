<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\StudentEnrollment;
use App\Services\GeneralSettingsService;
use App\Services\BrowsershotService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;

class GenerateAssessmentCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'assessment:generate {enrollment_id : The enrollment ID to generate assessment for}';

    /**
     * The console command description.
     */
    protected $description = 'Generate assessment PDF for a specific enrollment';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            $enrollmentId = $this->argument('enrollment_id');
            
            // Find the enrollment record (including soft-deleted)
            $enrollment = StudentEnrollment::withTrashed()
                ->with(['student', 'course', 'subjectsEnrolled.subject', 'studentTuition'])
                ->find($enrollmentId);

            if (!$enrollment) {
                $this->error("Enrollment with ID {$enrollmentId} not found.");
                return 1;
            }

            // Generate the assessment PDF synchronously
            $pdfPath = $this->generateAssessmentPdf($enrollment);

            if ($pdfPath && file_exists($pdfPath)) {
                // Output the path for the parent process to capture
                $this->line($pdfPath);
                return 0;
            } else {
                $this->error("Failed to generate PDF for enrollment {$enrollmentId}");
                return 1;
            }

        } catch (\Exception $e) {
            $this->error("Error generating assessment: " . $e->getMessage());
            Log::error('Assessment generation command failed', [
                'enrollment_id' => $this->argument('enrollment_id'),
                'exception' => $e,
            ]);
            return 1;
        }
    }

    /**
     * Generate assessment PDF synchronously
     */
    private function generateAssessmentPdf(StudentEnrollment $enrollment): ?string
    {
        try {
            $settingsService = app(GeneralSettingsService::class);
            
            // Prepare data for PDF generation (matching the expected structure)
            $data = [
                'student' => $enrollment,
                'subjects' => $enrollment->SubjectsEnrolled,
                'school_year' => mb_convert_encoding(
                    $settingsService->getCurrentSchoolYearString() ?? '',
                    'UTF-8',
                    'auto'
                ),
                'semester' => mb_convert_encoding(
                    $settingsService->getAvailableSemesters()[$settingsService->getCurrentSemester()] ?? '',
                    'UTF-8',
                    'auto'
                ),
                'tuition' => $enrollment->studentTuition,
                'general_settings' => $settingsService->getGlobalSettingsModel(),
            ];

            // Generate unique filename
            $randomChars = substr(str_shuffle('0123456789abcdefghijklmnopqrstuvwxyz'), 0, 8);
            $filename = "assessment-{$enrollment->id}-{$randomChars}.pdf";
            
            // Ensure directories exist
            $privateDir = storage_path('app/private');
            if (!File::exists($privateDir)) {
                File::makeDirectory($privateDir, 0755, true);
            }

            $pdfPath = $privateDir . DIRECTORY_SEPARATOR . $filename;

            // Render HTML
            $html = view('pdf.assesment-form', $data)->render();

            // Generate PDF using BrowsershotService
            $success = BrowsershotService::generatePdf($html, $pdfPath, [
                'format' => 'A4',
                'landscape' => true,
                'print_background' => true,
                'margin_top' => 10,
                'margin_bottom' => 10,
                'margin_left' => 10,
                'margin_right' => 10,
                'timeout' => 120,
                'wait_until_network_idle' => false,
            ]);

            if ($success && file_exists($pdfPath)) {
                // Create resource record
                $enrollment->resources()->create([
                    'resourceable_id' => $enrollment->id,
                    'resourceable_type' => $enrollment::class,
                    'type' => 'assessment',
                    'file_path' => $pdfPath,
                    'file_name' => $filename,
                    'mime_type' => 'application/pdf',
                    'disk' => 'local',
                    'file_size' => filesize($pdfPath),
                    'metadata' => [
                        'school_year' => $settingsService->getCurrentSchoolYearString(),
                        'semester' => $settingsService->getAvailableSemesters()[$settingsService->getCurrentSemester()] ?? '',
                        'generation_method' => 'concurrent_command',
                        'generated_at' => now()->toISOString(),
                    ],
                ]);

                return $pdfPath;
            }

            return null;

        } catch (\Exception $e) {
            Log::error('PDF generation failed in command', [
                'enrollment_id' => $enrollment->id,
                'exception' => $e,
            ]);
            throw $e;
        }
    }
}
