<?php

declare(strict_types=1);

namespace App\Infolists\Components;

use Filament\Infolists\Components\Entry;
use Illuminate\Database\Eloquent\Collection;

final class Timetable extends Entry
{
    protected string $view = 'infolists.components.timetable';

    /**
     * Get efficiently loaded schedules with room relationship
     */
    public function getSchedules(): Collection
    {
        return $this->getRecord()->Schedule()->with('room')->get();
    }

    /**
     * Get subject code for the class
     */
    public function getSubjectCode(): string
    {
        return $this->getRecord()->subject_code ?? 'N/A';
    }

    /**
     * Get formatted schedule summary for quick display
     */
    public function getScheduleSummary(): array
    {
        $schedules = $this->getSchedules();

        if ($schedules->isEmpty()) {
            return [
                'total_sessions' => 0,
                'days' => [],
                'time_range' => null,
            ];
        }

        $days = $schedules->pluck('day_of_week')->unique()->sort()->values()->toArray();
        $times = $schedules->map(function ($schedule) {
            return [
                'start' => $schedule->start_time,
                'end' => $schedule->end_time,
            ];
        });

        $earliestStart = $times->min('start');
        $latestEnd = $times->max('end');

        return [
            'total_sessions' => $schedules->count(),
            'days' => $days,
            'time_range' => $earliestStart && $latestEnd
                ? $earliestStart->format('g:i A') . ' - ' . $latestEnd->format('g:i A')
                : null,
        ];
    }

    /**
     * Check if class has any scheduled sessions
     */
    public function hasSchedule(): bool
    {
        return $this->getSchedules()->isNotEmpty();
    }
}
