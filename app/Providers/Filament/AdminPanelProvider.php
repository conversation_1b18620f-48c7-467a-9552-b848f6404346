<?php

namespace App\Providers\Filament;

use Filament\Pages;
use Filament\Panel;
use Filament\Widgets;
use Livewire\Livewire;
use Filament\PanelProvider;
use Filament\Navigation\MenuItem;
use App\Filament\Pages\AdminLogin;
use Filament\Support\Colors\Color;
use Illuminate\Support\HtmlString;
use Filament\View\PanelsRenderHook;
use Filafly\PhosphorIconReplacement;
use Rawilk\ProfileFilament\Features;
use Illuminate\Support\Facades\Blade;
use App\Filament\Pages\GeneralSetting;
use Filament\Navigation\NavigationGroup;
use Rupadana\ApiService\ApiServicePlugin;
use Filament\Http\Middleware\Authenticate;
use Teguh02\FilamentDbSync\FilamentDbSync;
use App\Filament\Pages\StudentReportingPage;
use Vormkracht10\FilamentMails\FilamentMails;
use App\Filament\Resources\TransactionResource;
use Illuminate\Session\Middleware\StartSession;
use Modules\Inventory\Filament\InventoryPlugin;
use Devonab\FilamentEasyFooter\EasyFooterPlugin;
use Illuminate\Cookie\Middleware\EncryptCookies;
use App\Filament\Resources\GradeApprovalResource;
use Bytexr\QueueableBulkActions\Enums\StatusEnum;
use Filament\Http\Middleware\AuthenticateSession;
use Rawilk\ProfileFilament\ProfileFilamentPlugin;
use Vormkracht10\FilamentMails\FilamentMailsPlugin;
use DiscoveryDesign\FilamentGaze\FilamentGazePlugin;
use Guava\FilamentKnowledgeBase\KnowledgeBasePlugin;
use Monzer\FilamentChatifyIntegration\ChatifyPlugin;
use BezhanSalleh\FilamentShield\FilamentShieldPlugin;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Andreia\FilamentNordTheme\FilamentNordThemePlugin;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use BezhanSalleh\FilamentShield\Resources\RoleResource;
use Filament\Http\Middleware\DisableBladeIconComponents;
use CharrafiMed\GlobalSearchModal\GlobalSearchModalPlugin;
use Cmsmaxinc\FilamentErrorPages\FilamentErrorPagesPlugin;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Njxqlus\FilamentProgressbar\FilamentProgressbarPlugin;
use Bytexr\QueueableBulkActions\QueueableBulkActionsPlugin;
use Filament\Navigation\NavigationItem;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Joaopaulolndev\FilamentGeneralSettings\FilamentGeneralSettingsPlugin;
use ShuvroRoy\FilamentSpatieLaravelBackup\FilamentSpatieLaravelBackupPlugin;

final class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        // Render the new Livewire component for semester and school year selection
        $panel->renderHook(
            'panels::global-search.before',
            fn () => // Return a view instance containing the Livewire component
            view('livewire.semester-school-year-selector')
        );

        $panel->renderHook(
            PanelsRenderHook::AUTH_LOGIN_FORM_BEFORE,
            fn (): string => Blade::render(
                config('app.enable_faculty_panel')
                    ? '<x-filament::link href="/faculty/login" class="fi-link text-sm">Faculty Login</x-filament::link>'
                    : ''
            )
        );

        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->spa()
            ->brandName('DCCP administrator')
            ->favicon(asset('favicon.ico'))
            ->login(AdminLogin::class)
            ->emailVerification()
            ->passwordReset()
            ->unsavedChangesAlerts()
            // ->viteTheme('resources/css/filament/admin/theme.css')
            ->databaseNotifications()
            ->colors([
                'primary' => Color::Amber,
            ])
            ->discoverResources(
                in: app_path('Filament/Resources'),
                for: 'App\\Filament\\Resources'
            )
            ->resources([GradeApprovalResource::class])
            ->discoverPages(
                in: app_path('Filament/Pages'),
                for: 'App\\Filament\\Pages'
            )
            ->pages([Pages\Dashboard::class, StudentReportingPage::class])
            ->discoverWidgets(
                in: app_path('Filament/Widgets'),
                for: 'App\\Filament\\Widgets'
            )
            ->widgets([
                Widgets\AccountWidget::class,
                Widgets\FilamentInfoWidget::class,
                // // \App\Filament\Widgets\StudentStatsWidget::class,
                // \App\Filament\Widgets\EnrollmentOverviewWidget::class,
                // // \App\Filament\Widgets\CourseDistributionWidget::class,
                // // \App\Filament\Widgets\FinancialOverviewWidget::class,
                // \App\Filament\Widgets\ClassStatsWidget::class,
            ])
            ->plugins([
                FilamentSpatieLaravelBackupPlugin::make(),
                // FilamentDbSync::make(),
                QueueableBulkActionsPlugin::make()
                    // ->bulkActionModel(YourBulkActionModel::class) // (optional) - Allows you to register your own model which extends \Bytexr\QueueableBulkActions\Models\BulkAction
                    // ->bulkActionRecordModel(YourBulkActionRecordModel::class) // (optional) - Allows you to register your own model for records which extends \Bytexr\QueueableBulkActions\Models\BulkActionRecord
                    // ->renderHook(
                    //     PanelsRenderHook::RESOURCE_PAGES_LIST_RECORDS_TABLE_BEFORE
                    // ) // (optional) - Allows you to change where notification is rendered, multiple render hooks can be passed as array [Default: PanelsRenderHook::RESOURCE_PAGES_LIST_RECORDS_TABLE_BEFORE]
                    ->pollingInterval('5s') // (optional) - Allows you to change or disable polling interval, set to null to disable. [Default: 5s]
                    ->queue('redis', 'default') // (optional) - Allows you to change which connection and queue should be used [Default: env('QUEUE_CONNECTION'), default]
                    // ->resource(YourBulkActionResource::class) // (optional) - Allows you to change which resource should be used to display historical bulk actions
                    ->colors([
                        StatusEnum::QUEUED->value => 'slate',
                        StatusEnum::IN_PROGRESS->value => 'info',
                        StatusEnum::FINISHED->value => 'success',
                        StatusEnum::FAILED->value => 'danger',
                    ]),
                ProfileFilamentPlugin::make()->features(
                    Features::defaults()->twoFactorAuthentication(
                        enabled: true,
                        authenticatorApps: true,
                        webauthn: true,
                        passkeys: true
                    )
                ),
                KnowledgeBasePlugin::make(),
                FilamentNordThemePlugin::make(),
                ChatifyPlugin::make(),
                FilamentProgressbarPlugin::make(),
                // DashStackThemePlugin::make(),
                // ->features(
                //     Features::defaults()->useSudoMode(false)
                // ),
                PhosphorIconReplacement::make()->regular(),
                EasyFooterPlugin::make()
                    ->withLoadTime()
                    ->withBorder()
                    ->withSentence(
                        new HtmlString(
                            '<img src="https://static.cdnlogo.com/logos/l/23/laravel.svg" style="margin-right:.5rem;" alt="Laravel Logo" width="20" height="20"> Laravel'
                        )
                    )
                    ->withGithub(showLogo: true, showUrl: true),
                FilamentMailsPlugin::make(),
                FilamentGazePlugin::make(),
                // GlobalSearchModalPlugin::make(),
                FilamentErrorPagesPlugin::make(),
                FilamentShieldPlugin::make(),
                // FilamentNordThemePlugin::make(),
                ApiServicePlugin::make(),
                // FilamentGeneralSettingsPlugin::make(),
                InventoryPlugin::make(),
            ])
            ->navigationGroups([
                NavigationGroup::make()
                ->label('College Students')
                ->icon('heroicon-o-user-group')
                ->collapsed('true'),
                 NavigationGroup::make()
                 ->label('Academic Management')
                 ->icon('heroicon-o-book-open')
                 ->collapsed(true),
                 NavigationGroup::make()
                 ->label('User Management')
                 ->icon('heroicon-o-users')
                 ->collapsed(true),
            
            ])
            ->routes(fn () => FilamentMails::routes())
            ->userMenuItems([
                // MenuItem::make()
                //     ->label("Transactions")
                //     ->url(fn(): string => AdminTransactionsResource::getUrl())
                //     ->icon("heroicon-o-banknotes"),
                MenuItem::make()
                    ->label('Portal Settings')
                    ->url(fn (): string => GeneralSetting::getUrl())

                    ->icon('heroicon-o-server'),
                MenuItem::make()
                    ->label('Roles')
                    ->url(fn (): string => RoleResource::getUrl())
                    ->icon('heroicon-o-shield-check'),
                MenuItem::make()
                    ->label('Transactions')
                    ->url(fn (): string => TransactionResource::getUrl())
                    ->icon('heroicon-o-banknotes'),
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([Authenticate::class]);
    }
}
