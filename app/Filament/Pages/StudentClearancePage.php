<?php

declare(strict_types=1);

namespace App\Filament\Pages;

use App\Models\GeneralSetting;
use App\Models\Student;
use App\Models\StudentClearance;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;

final class StudentClearancePage extends Page implements HasTable
{
    use InteractsWithTable;

    // protected static ?string $navigationIcon = 'heroicon-o-check-circle';

    protected static string $view = 'filament.pages.student-clearance-page';

    protected static ?string $navigationLabel = 'Student Clearances';

    protected static ?string $title = 'Student Clearance Management';

    protected static ?int $navigationSort = 15;

    protected static ?string $navigationGroup = 'College Students';

    public function table(Table $table): Table
    {
        $settings = GeneralSetting::first();

        return $table
            ->query(
                Student::query()
                    ->select('students.*')
                    ->addSelect([
                        'is_cleared' => StudentClearance::query()
                            ->selectRaw('is_cleared')
                            ->whereColumn('student_id', 'students.id')
                            ->where('academic_year', $settings->getSchoolYear())
                            ->where('semester', $settings->semester)
                            ->limit(1),
                    ])
                    ->addSelect([
                        'clearance_id' => StudentClearance::query()
                            ->selectRaw('id')
                            ->whereColumn('student_id', 'students.id')
                            ->where('academic_year', $settings->getSchoolYear())
                            ->where('semester', $settings->semester)
                            ->limit(1),
                    ])
            )
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('full_name')
                    ->label('Name')
                    ->searchable(query: fn (Builder $query, string $search): Builder => $query->whereRaw(
                        "LOWER(CONCAT(last_name, ', ', first_name, ' ', middle_name)) LIKE ?",
                        ['%'.mb_strtolower($search).'%']
                    )),
                TextColumn::make('course.code')
                    ->label('Course')
                    ->sortable(),
                TextColumn::make('academic_year')
                    ->label('Year Level')
                    ->formatStateUsing(fn ($state): string => match ($state) {
                        1 => '1st Year',
                        2 => '2nd Year',
                        3 => '3rd Year',
                        4 => '4th Year',
                        5 => 'Graduate',
                        default => 'Unknown',
                    }),
                IconColumn::make('is_cleared')
                    ->label('Clearance Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
            ])
            ->filters([
                SelectFilter::make('course_id')
                    ->label('Course')
                    ->relationship('course', 'code'),
                SelectFilter::make('academic_year')
                    ->label('Year Level')
                    ->options([
                        1 => '1st Year',
                        2 => '2nd Year',
                        3 => '3rd Year',
                        4 => '4th Year',
                        5 => 'Graduate',
                    ]),
                SelectFilter::make('clearance_status')
                    ->label('Clearance Status')
                    ->options([
                        '1' => 'Cleared',
                        '0' => 'Not Cleared',
                    ])
                    ->query(function (Builder $query, array $data) use ($settings): Builder {
                        if (! isset($data['value']) || $data['value'] === '') {
                            return $query;
                        }

                        $isCleared = $data['value'] === '1';

                        return $query->whereHas('clearances', fn (Builder $query): Builder => $query->where('academic_year', $settings->getSchoolYear())
                            ->where('semester', $settings->semester)
                            ->where('is_cleared', $isCleared));
                    }),
            ])
            ->actions([
                Action::make('manageClearance')
                    ->label('Manage Clearance')
                    ->icon('heroicon-o-clipboard-document-check')
                    ->form([
                        Select::make('status')
                            ->label('Clearance Status')
                            ->options([
                                '1' => 'Cleared',
                                '0' => 'Not Cleared',
                            ])
                            ->required(),
                        Textarea::make('remarks')
                            ->label('Remarks')
                            ->maxLength(255),
                    ])
                    ->action(function (array $data, $record): void {
                        $user = Auth::user();
                        $clearedBy = $user ? $user->name : 'System';

                        if ($data['status'] === '1') {
                            $record->markClearanceAsCleared($clearedBy, $data['remarks'] ?? null);
                            Notification::make()
                                ->title('Clearance Updated')
                                ->body('Student has been marked as cleared.')
                                ->success()
                                ->send();
                        } else {
                            $record->markClearanceAsNotCleared($data['remarks'] ?? null);
                            Notification::make()
                                ->title('Clearance Updated')
                                ->body('Student has been marked as not cleared.')
                                ->warning()
                                ->send();
                        }
                    }),
            ])
            ->bulkActions([
                BulkAction::make('bulkClearStudents')
                    ->label('Mark as Cleared')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->action(function (Collection $records): void {
                        $user = Auth::user();
                        $clearedBy = $user ? $user->name : 'System';
                        $count = 0;

                        foreach ($records as $record) {
                            if ($record->markClearanceAsCleared($clearedBy)) {
                                $count++;
                            }
                        }

                        Notification::make()
                            ->title('Bulk Clearance Update')
                            ->body("{$count} students have been marked as cleared.")
                            ->success()
                            ->send();
                    }),
                BulkAction::make('bulkUnClearStudents')
                    ->label('Mark as Not Cleared')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->action(function (Collection $records): void {
                        $count = 0;

                        foreach ($records as $record) {
                            if ($record->markClearanceAsNotCleared()) {
                                $count++;
                            }
                        }

                        Notification::make()
                            ->title('Bulk Clearance Update')
                            ->body("{$count} students have been marked as not cleared.")
                            ->warning()
                            ->send();
                    }),
            ])
            ->defaultPaginationPageOption(15)
            ->defaultSort('id', 'desc');
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('generateClearances')
                ->label('Generate Clearances')
                ->icon('heroicon-o-plus-circle')
                ->color('success')
                ->action(function (): void {
                    // Run the command to generate clearances
                    $exitCode = \Illuminate\Support\Facades\Artisan::call('students:generate-clearances');

                    if ($exitCode === 0) {
                        Notification::make()
                            ->title('Clearances Generated')
                            ->body('Student clearance records have been generated successfully.')
                            ->success()
                            ->send();
                    } else {
                        Notification::make()
                            ->title('Error')
                            ->body('There was an error generating the clearance records.')
                            ->danger()
                            ->send();
                    }
                }),
        ];
    }
}
